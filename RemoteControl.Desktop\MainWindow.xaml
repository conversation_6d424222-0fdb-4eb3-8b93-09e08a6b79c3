<Window x:Class="RemoteControl.Desktop.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Remote Control - 无线触控板服务器"
        Height="500"
        Width="600"
        WindowStartupLocation="CenterScreen"
        Background="#2C3E50">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0"
                   Text="Remote Control Server"
                   FontSize="24"
                   FontWeight="Bold"
                   Foreground="White"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"/>

        <!-- 服务器状态 -->
        <Border Grid.Row="1"
                Background="#34495E"
                CornerRadius="10"
                Padding="20"
                Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="服务器状态"
                           FontSize="16"
                           FontWeight="SemiBold"
                           Foreground="White"
                           Margin="0,0,0,10"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Ellipse Grid.Column="0"
                             x:Name="StatusIndicator"
                             Width="12"
                             Height="12"
                             Fill="Red"
                             Margin="0,0,10,0"/>

                    <TextBlock Grid.Column="1"
                               x:Name="StatusText"
                               Text="服务器已停止"
                               Foreground="White"
                               VerticalAlignment="Center"/>
                </Grid>
            </StackPanel>
        </Border>

        <!-- 控制按钮 -->
        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Center"
                    Margin="0,0,0,20">

            <Button x:Name="StartStopButton"
                    Content="启动服务器"
                    Width="120"
                    Height="40"
                    Background="#3498DB"
                    Foreground="White"
                    BorderThickness="0"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Margin="0,0,10,0"
                    Click="StartStopButton_Click">
                <Button.Style>
                    <Style TargetType="Button">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                            CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver"
                                     Value="True">
                                <Setter Property="Background"
                                        Value="#2980B9"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>

            <Button x:Name="OpenBrowserButton"
                    Content="打开网页"
                    Width="120"
                    Height="40"
                    Background="#27AE60"
                    Foreground="White"
                    BorderThickness="0"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Margin="0,0,10,0"
                    Click="OpenBrowserButton_Click">
                <Button.Style>
                    <Style TargetType="Button">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                            CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver"
                                     Value="True">
                                <Setter Property="Background"
                                        Value="#229954"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>

            <Button x:Name="SettingsButton"
                    Content="⚙️ 设置"
                    Width="100"
                    Height="40"
                    Background="#9B59B6"
                    Foreground="White"
                    BorderThickness="0"
                    FontSize="14"
                    FontWeight="SemiBold"
                    Click="SettingsButton_Click">
                <Button.Style>
                    <Style TargetType="Button">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                            CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver"
                                     Value="True">
                                <Setter Property="Background"
                                        Value="#8E44AD"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
        </StackPanel>

        <!-- 连接信息 -->
        <Border Grid.Row="3"
                Background="#34495E"
                CornerRadius="10"
                Padding="20">
            <StackPanel>
                <TextBlock Text="连接信息"
                           FontSize="16"
                           FontWeight="SemiBold"
                           Foreground="White"
                           Margin="0,0,0,15"/>

                <TextBlock x:Name="IpAddressText"
                           Text="服务器地址: 未启动"
                           Foreground="#BDC3C7"
                           FontFamily="Consolas"
                           FontSize="14"
                           Margin="0,0,0,10"/>

                <TextBlock x:Name="ConnectedClientsText"
                           Text="已连接设备: 0"
                           Foreground="#BDC3C7"
                           FontSize="14"
                           Margin="0,0,0,15"/>

                <TextBlock Text="使用说明:"
                           FontWeight="SemiBold"
                           Foreground="White"
                           Margin="0,0,0,5"/>

                <TextBlock Text="1. 点击'启动服务器'开始服务"
                           Foreground="#BDC3C7"
                           FontSize="12"
                           Margin="0,0,0,3"/>

                <TextBlock Text="2. 确保手机和电脑在同一WiFi网络"
                           Foreground="#BDC3C7"
                           FontSize="12"
                           Margin="0,0,0,3"/>

                <TextBlock Text="3. 在手机浏览器中访问显示的地址"
                           Foreground="#BDC3C7"
                           FontSize="12"
                           Margin="0,0,0,3"/>

                <TextBlock Text="4. 即可使用手机作为触控板和键盘"
                           Foreground="#BDC3C7"
                           FontSize="12"/>
            </StackPanel>
        </Border>

        <!-- 底部信息 -->
        <TextBlock Grid.Row="4"
                   Text="Remote Control v1.0 - 简单可靠的无线触控板"
                   Foreground="#7F8C8D"
                   FontSize="12"
                   HorizontalAlignment="Center"
                   Margin="0,20,0,0"/>
    </Grid>
</Window>
