using System.Windows;
using RemoteControl.Desktop.Models;
using RemoteControl.Desktop.Services;

namespace RemoteControl.Desktop
{
    public partial class SettingsWindow : Window
    {
        private readonly SettingsService _settingsService;
        private readonly AppSettings _originalSettings;

        public SettingsWindow(SettingsService settingsService)
        {
            InitializeComponent();
            _settingsService = settingsService;

            // 创建设置的副本以便取消时恢复
            _originalSettings = new AppSettings
            {
                Port = settingsService.Settings.Port,
                AutoStart = settingsService.Settings.AutoStart,
                StartWithWindows = settingsService.Settings.StartWithWindows,
                MinimizeToTray = settingsService.Settings.MinimizeToTray,
                MouseSensitivity = settingsService.Settings.MouseSensitivity,
                ScrollSensitivity = settingsService.Settings.ScrollSensitivity
            };

            // 绑定数据上下文
            DataContext = settingsService.Settings;
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 验证端口号
                if (_settingsService.Settings.Port < 1 || _settingsService.Settings.Port > 65535)
                {
                    MessageBox.Show("端口号必须在1-65535之间", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                _settingsService.SaveSettings();
                MessageBox.Show("设置已保存", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                DialogResult = true;
                Close();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            // 恢复原始设置
            _settingsService.Settings.Port = _originalSettings.Port;
            _settingsService.Settings.AutoStart = _originalSettings.AutoStart;
            _settingsService.Settings.StartWithWindows = _originalSettings.StartWithWindows;
            _settingsService.Settings.MinimizeToTray = _originalSettings.MinimizeToTray;
            _settingsService.Settings.MouseSensitivity = _originalSettings.MouseSensitivity;
            _settingsService.Settings.ScrollSensitivity = _originalSettings.ScrollSensitivity;

            DialogResult = false;
            Close();
        }

        private void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要重置所有设置为默认值吗？", "确认重置",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                _settingsService.ResetToDefaults();
                DataContext = _settingsService.Settings;
                MessageBox.Show("设置已重置为默认值", "重置完成", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }
}
